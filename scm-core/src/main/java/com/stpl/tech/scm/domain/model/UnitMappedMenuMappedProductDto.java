/*
 * Created By Shanmukh
 */

package com.stpl.tech.scm.domain.model;

import lombok.Data;

/**
 * DTO for unit mapped and menu mapped products
 * Represents products that are both unit-mapped and menu-mapped
 */
@Data
public class UnitMappedMenuMappedProductDto {
    
    private Integer unitId;
    private Integer productId;
    private Integer rlId;
    private String dimension;
    private String categoryName;
    private String productClassification;
    private Integer productType;
    private String productName;
    private Integer brandId;
    private String isInventoryTracked;
    private String inventoryTrackLevel;
    
}
