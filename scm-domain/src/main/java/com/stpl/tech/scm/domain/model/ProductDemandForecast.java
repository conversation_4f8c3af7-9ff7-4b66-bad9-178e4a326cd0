package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProductDemandForecast {
    
    private SafetyStockForecastResult safetyStockForecastResult;
    private Map<java.util.Date, DemandForecastResult> demandForecastByDate;
    private String categoryName;
}
