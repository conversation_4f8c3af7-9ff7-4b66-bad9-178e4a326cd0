package com.stpl.tech.scm.domain.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DemandForecastingStrategyMetadataDto {

    private Integer id;
    private String strategyName;
    private Integer historicalDays;
    private Integer maxHistoricalDaysLookup;
    private String pastWeeksLookupType;
    private Integer noOfPastWeeks;
    private Integer safetyWeeks;
    private String safetyStockStrategy;
    private BigDecimal highProductSafetyValue;
    private BigDecimal highMaxStdDeviation;
    private BigDecimal mediumProductSafetyValue;
    private BigDecimal mediumMaxStdDeviation;
    private BigDecimal lowProductSafetyValue;
    private BigDecimal lowMaxStdDeviation;
    private BigDecimal nextDaySalesConsiderationPercentage;
    private String enableHighDayWiseSaleEdit;
    private String enableMediumDayWiseSaleEdit;
    private String enableLowDayWiseSaleEdit;
    private String enableHighSafetyStockEdit;
    private String enableMediumSafetyStockEdit;
    private String enableLowSafetyStockEdit;
    private Integer bigDayMultiplier;
    private BigDecimal highQuantile;
    private BigDecimal lowQuantile;
    private Integer stockOutThresholdInMinutes;
    private String applicableProductTypes;
}
