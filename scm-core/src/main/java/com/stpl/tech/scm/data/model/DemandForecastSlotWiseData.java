package com.stpl.tech.scm.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "DEMAND_FORECAST_SLOT_WISE_DATA")
public class DemandForecastSlotWiseData {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Integer slotId;

    @ManyToOne(fetch = FetchType.LAZY, targetEntity = DemandForecastPredictionData.class, optional = false)
    @JoinColumn(name = "DEMAND_FORECAST_PREDICTION_ID", nullable = false)
    private DemandForecastPredictionData demandForecastPredictionDataId;

    @Column(name ="BUSINESS_DATE")
    private Date businessDate;

    //adjusted sales data
    @Column(name = "ADJUSTED_TOTAL_QUANTITY_SOLD")
    private BigDecimal adjustedTotalQuantitySold;
    @Column(name = "ADJUSTED_BREAKFAST_SALES")
    private Integer adjustedBreakfastSales;
    @Column(name = "ADJUSTED_LUNCH_SALES")
    private Integer adjustedLunchSales;
    @Column(name = "ADJUSTED_EVENING_SALES")
    private Integer adjustedEveningSales;
    @Column(name = "ADJUSTED_DINNER_SALES")
    private Integer adjustedDinnerSales;
    @Column(name = "ADJUSTED_POST_DINNER_SALES")
    private Integer adjustedPostDinnerSales;
    @Column(name = "ADJUSTED_OVERNIGHT_SALES")
    private Integer adjustedOvernightSales;

    // original sales data
    @Column(name = "BREAKFAST_SALES")
    private Integer breakfastSales;
    @Column(name = "LUNCH_SALES")
    private Integer lunchSales;
    @Column(name = "EVENING_SALES")
    private Integer eveningSales;
    @Column(name = "DINNER_SALES")
    private Integer dinnerSales;
    @Column(name = "POST_DINNER_SALES")
    private Integer postDinnerSales;
    @Column(name = "OVERNIGHT_SALES")
    private Integer overnightSales;
    @Column(name = "TOTAL_QUANTITY_SOLD")
    private BigDecimal totalQuantitySold;

    // stockout data
    @Column(name = "BREAKFAST_STOCK_OUT_MINUTES")
    private Integer breakfastStockOutMinutes;
    @Column(name = "LUNCH_STOCK_OUT_MINUTES")
    private Integer lunchStockOutMinutes;
    @Column(name = "EVENING_STOCK_OUT_MINUTES")
    private Integer eveningStockOutMinutes;
    @Column(name = "DINNER_STOCK_OUT_MINUTES")
    private Integer dinnerStockOutMinutes;
    @Column(name = "POST_DINNER_STOCK_OUT_MINUTES")
    private Integer postDinnerStockOutMinutes;
    @Column(name = "OVERNIGHT_STOCK_OUT_MINUTES")
    private Integer overnightStockOutMinutes;
    @Column(name = "TOTAL_STOCK_OUT_IN_MINUTES_FOR_THAT_DAY")
    private BigDecimal totalStockOutInMinutesForThatDay;
    @Column(name = "PRODUCT_IN_STOCK_TIME")
    private BigDecimal productInStockTime;
    @Column(name = "TOTAL_CAFE_OPENING_TIME_IN_MINUTES_FOR_THAT_DAY")
    private Integer totalCafeOpeningTimeInMinutesForThatDay;

    //all slot columns
    @Column(name = "BREAKFAST_SLOT_MEAN")
    private BigDecimal breakfastSlotMean;
    @Column(name = "BREAKFAST_SLOT_STANDARD_DEVIATION")
    private BigDecimal breakfastSlotStandardDeviation;
    @Column(name = "BREAKFAST_SLOT_METHOD")
    private String breakfastSlotMethod;
    @Column(name = "LUNCH_SLOT_MEAN")
    private BigDecimal lunchSlotMean;
    @Column(name = "LUNCH_SLOT_STANDARD_DEVIATION")
    private BigDecimal lunchSlotStandardDeviation;
    @Column(name = "LUNCH_SLOT_METHOD")
    private String lunchSlotMethod;
    @Column(name = "EVENING_SLOT_MEAN")
    private BigDecimal eveningSlotMean;
    @Column(name = "EVENING_SLOT_STANDARD_DEVIATION")
    private BigDecimal eveningSlotStandardDeviation;
    @Column(name = "EVENING_SLOT_METHOD")
    private String eveningSlotMethod;
    @Column(name = "DINNER_SLOT_MEAN")
    private BigDecimal dinnerSlotMean;
    @Column(name = "DINNER_SLOT_STANDARD_DEVIATION")
    private BigDecimal dinnerSlotStandardDeviation;
    @Column(name = "DINNER_SLOT_METHOD")
    private String dinnerSlotMethod;
    @Column(name = "POST_DINNER_SLOT_MEAN")
    private BigDecimal postDinnerSlotMean;
    @Column(name = "POST_DINNER_SLOT_STANDARD_DEVIATION")
    private BigDecimal postDinnerSlotStandardDeviation;
    @Column(name = "POST_DINNER_SLOT_METHOD")
    private String postDinnerSlotMethod;
    @Column(name = "OVERNIGHT_SLOT_MEAN")
    private BigDecimal overnightSlotMean;
    @Column(name = "OVERNIGHT_SLOT_STANDARD_DEVIATION")
    private BigDecimal overnightSlotStandardDeviation;
    @Column(name = "OVERNIGHT_SLOT_METHOD")
    private String overnightSlotMethod;

}