//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.06.11 at 12:23:12 PM IST 
//

package com.stpl.tech.scm.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <p>
 * Java class for ReferenceOrderMenuItem complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="ReferenceOrderMenuItem"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="productId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="productName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="dimension" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="requestedQuantity" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="requestedAbsoluteQuantity" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="transferredQuantity" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="receivedQuantity" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="variants" type="{http://www.w3schools.com}ReferenceOrderMenuVariant" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ReferenceOrderMenuItem", propOrder = { "id", "productId", "productName", "dimension",
		"requestedQuantity", "requestedAbsoluteQuantity", "transferredQuantity", "receivedQuantity", "variants" })
public class  ReferenceOrderMenuItem {

	@XmlElement(required = true, type = Integer.class, nillable = true)
	protected Integer id;
	protected int productId;
	@XmlElement(required = true)
	protected String productName;
	@XmlElement(required = true, nillable = true)
	protected String dimension;
	protected float requestedQuantity;
	protected float requestedAbsoluteQuantity;
	@XmlElement(required = true, type = Float.class, nillable = true)
	protected Float transferredQuantity;
	@XmlElement(required = true, type = Float.class, nillable = true)
	protected Float receivedQuantity;
	@XmlElement(required = true, type = Float.class, nillable = true)
	protected Float quantity;
	@XmlElement(required = true, type = Float.class, nillable = true)
	protected Float saleQuantity;
	@XmlElement(required = true, type = Float.class, nillable = true)
	protected Float dineInQuantity;
	@XmlElement(required = true, type = Float.class, nillable = true)
	protected Float deliveryQuantity;
	@XmlElement(required = true, type = Float.class, nillable = true)
	protected Float takeawayQuantity;
	@XmlElement(nillable = true)
	protected List<ReferenceOrderMenuVariant> variants;
	protected Map<String,Float> dateOrderings;
	protected Map<String,Float> dateRemaining;
	protected Map<String,Float> originalDateRemaining;
	protected Map<String,Float> originalDateOrderings;
	protected BigDecimal originalOrderingQuantity;
	protected BigDecimal originalSaleQuantity;
	protected Map<String, Float> multiplierMap;
	protected BigDecimal safetyStockQuantity;
	protected BigDecimal standardDeviation;
	protected BigDecimal safetyStockMultiplier;
	protected MeanType method;
	protected ProductSaleClusterEnum productSaleCluster;
	protected Map<Date, DemandForecastResult> demandForecastByDate;
	/**
	 * Gets the value of the id property.
	 * 
	 * @return possible object is {@link Integer }
	 * 
	 */
	public Integer getId() {
		return id;
	}

	/**
	 * Sets the value of the id property.
	 * 
	 * @param value
	 *            allowed object is {@link Integer }
	 * 
	 */
	public void setId(Integer value) {
		this.id = value;
	}

	/**
	 * Gets the value of the productId property.
	 *
	 */
	public int getProductId() {
		return productId;
	}

	/**
	 * Sets the value of the productId property.
	 *
	 */
	public void setProductId(int value) {
		this.productId = value;
	}

	/**
	 * Gets the value of the productName property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getProductName() {
		return productName;
	}

	/**
	 * Sets the value of the productName property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setProductName(String value) {
		this.productName = value;
	}

	/**
	 * Gets the value of the dimension property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getDimension() {
		return dimension;
	}

	/**
	 * Sets the value of the dimension property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setDimension(String value) {
		this.dimension = value;
	}

	/**
	 * Gets the value of the requestedQuantity property.
	 * 
	 */
	public float getRequestedQuantity() {
		return requestedQuantity;
	}

	/**
	 * Sets the value of the requestedQuantity property.
	 * 
	 */
	public void setRequestedQuantity(float value) {
		this.requestedQuantity = value;
	}

	/**
	 * Gets the value of the requestedAbsoluteQuantity property.
	 * 
	 */
	public float getRequestedAbsoluteQuantity() {
		return requestedAbsoluteQuantity;
	}

	/**
	 * Sets the value of the requestedAbsoluteQuantity property.
	 * 
	 */
	public void setRequestedAbsoluteQuantity(float value) {
		this.requestedAbsoluteQuantity = value;
	}

	/**
	 * Gets the value of the transferredQuantity property.
	 * 
	 * @return possible object is {@link Float }
	 * 
	 */
	public Float getTransferredQuantity() {
		return transferredQuantity;
	}

	/**
	 * Sets the value of the transferredQuantity property.
	 * 
	 * @param value
	 *            allowed object is {@link Float }
	 * 
	 */
	public void setTransferredQuantity(Float value) {
		this.transferredQuantity = value;
	}

	/**
	 * Gets the value of the receivedQuantity property.
	 * 
	 * @return possible object is {@link Float }
	 * 
	 */
	public Float getReceivedQuantity() {
		return receivedQuantity;
	}

	/**
	 * Sets the value of the receivedQuantity property.
	 * 
	 * @param value
	 *            allowed object is {@link Float }
	 * 
	 */
	public void setReceivedQuantity(Float value) {
		this.receivedQuantity = value;
	}

	/**
	 * Gets the value of the variants property.
	 *
	 * <p>
	 * This accessor method returns a reference to the live list, not a
	 * snapshot. Therefore any modification you make to the returned list will
	 * be present inside the JAXB object. This is why there is not a
	 * <CODE>set</CODE> method for the variants property.
	 *
	 * <p>
	 * For example, to add a new item, do as follows:
	 * 
	 * <pre>
	 * getVariants().add(newItem);
	 * </pre>
	 *
	 *
	 * <p>
	 * Objects of the following type(s) are allowed in the list
	 * {@link ReferenceOrderMenuVariant }
	 *
	 *
	 */
	public List<ReferenceOrderMenuVariant> getVariants() {
		if (variants == null) {
			variants = new ArrayList<ReferenceOrderMenuVariant>();
		}
		return this.variants;
	}

	public void setVariants(List<ReferenceOrderMenuVariant> variants) {
		this.variants = variants;
	}

	public Float getQuantity() {
		return quantity;
	}

	public void setQuantity(Float quantity) {
		this.quantity = quantity;
	}

	public Float getDineInQuantity() {
		return dineInQuantity;
	}

	public void setDineInQuantity(Float percentageDineIn) {
		this.dineInQuantity = percentageDineIn;
	}

	public Float getDeliveryQuantity() {
		return deliveryQuantity;
	}

	public void setDeliveryQuantity(Float percentageDelivery) {
		this.deliveryQuantity = percentageDelivery;
	}

	public Float getTakeawayQuantity() {
		return takeawayQuantity;
	}

	public void setTakeawayQuantity(Float percentageTakeaway) {
		this.takeawayQuantity = percentageTakeaway;
	}

	public Float getSaleQuantity() {
		return saleQuantity;
	}

	public void setSaleQuantity(Float saleQuantity) {
		this.saleQuantity = saleQuantity;
	}

	public Map<String, Float> getDateOrderings() {
		if (Objects.isNull(this.dateOrderings)) {
			return new HashMap<>();
		}
		return dateOrderings;
	}

	public void setDateOrderings(Map<String, Float> dateOrderings) {
		this.dateOrderings = dateOrderings;
	}

	public Map<String, Float> getDateRemaining() {
		if (Objects.isNull(this.dateRemaining)) {
			return new HashMap<>();
		}
		return dateRemaining;
	}

	public void setDateRemaining(Map<String, Float> dateRemaining) {
		this.dateRemaining = dateRemaining;
	}

	public Map<String, Float> getOriginalDateOrderings() {
		if (Objects.isNull(this.originalDateOrderings)) {
			return new HashMap<>();
		}
		return originalDateOrderings;
	}

	public void setOriginalDateOrderings(Map<String, Float> originalDateOrderings) {
		this.originalDateOrderings = originalDateOrderings;
	}

	public Map<String, Float> getOriginalDateRemaining() {
		if (Objects.isNull(this.originalDateRemaining)) {
			return new HashMap<>();
		}
		return originalDateRemaining;
	}

	public void setOriginalDateRemaining(Map<String, Float> originalDateRemaining) {
		this.originalDateRemaining = originalDateRemaining;
	}

	public BigDecimal getOriginalOrderingQuantity() {
		return originalOrderingQuantity;
	}

	public void setOriginalOrderingQuantity(BigDecimal originalOrderingQuantity) {
		this.originalOrderingQuantity = originalOrderingQuantity;
	}

	public BigDecimal getOriginalSaleQuantity() {
		return originalSaleQuantity;
	}

	public void setOriginalSaleQuantity(BigDecimal originalSaleQuantity) {
		this.originalSaleQuantity = originalSaleQuantity;
	}

	public Map<String, Float> getMultiplierMap() {
		if (Objects.isNull(this.multiplierMap)) {
			return new HashMap<>();
		}
		return multiplierMap;
	}

	public void setMultiplierMap(Map<String, Float> multiplierMap) {
		this.multiplierMap = multiplierMap;
	}

	public BigDecimal getSafetyStockQuantity() {
		return safetyStockQuantity;
	}

	public void setSafetyStockQuantity(BigDecimal safetyStockQuantity) {
		this.safetyStockQuantity = safetyStockQuantity;
	}

	public BigDecimal getStandardDeviation() {
		return standardDeviation;
	}

	public void setStandardDeviation(BigDecimal standardDeviation) {
		this.standardDeviation = standardDeviation;
	}

	public BigDecimal getSafetyStockMultiplier() {
		return safetyStockMultiplier;
	}

	public void setSafetyStockMultiplier(BigDecimal safetyStockMultiplier) {
		this.safetyStockMultiplier = safetyStockMultiplier;
	}

	public MeanType getMethod() {
		return method;
	}

	public void setMethod(MeanType method) {
		this.method = method;
	}

	public ProductSaleClusterEnum getProductSaleCluster() {
		return productSaleCluster;
	}

	public void setProductSaleCluster(ProductSaleClusterEnum productSaleCluster) {
		this.productSaleCluster = productSaleCluster;
	}

	public Map<Date, DemandForecastResult> getDemandForecastResults() {
		return demandForecastByDate;
	}

	public void setDemandForecastResults(Map<Date, DemandForecastResult> demandForecastByDate) {
		this.demandForecastByDate = demandForecastByDate;
	}
}
