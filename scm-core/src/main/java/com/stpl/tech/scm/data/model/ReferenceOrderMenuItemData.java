package com.stpl.tech.scm.data.model;

import com.stpl.tech.scm.domain.model.MeanType;
import com.stpl.tech.scm.domain.model.ProductSaleClusterEnum;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

/**
 * Created by <PERSON><PERSON> on 10-06-2016.
 */
@Entity
@Table(name = "REFERENCE_ORDER_MENU_ITEM")
public class ReferenceOrderMenuItemData {

    private Integer id;
    private int productId;
    private String productName;
    private String dimension;
    private BigDecimal requestedQuantity;
    private BigDecimal requestedAbsoluteQuantity;
    private BigDecimal transferredQuantity;
    private BigDecimal receivedQuantity;
    private BigDecimal quantity;
    private BigDecimal dineInQuantity;
    private BigDecimal deliveryQuantity;
    private BigDecimal takeawayQuantity;
    private List<ROMenuItemVariantData> variants = new ArrayList<ROMenuItemVariantData>(0);
    private ReferenceOrderData referenceOrderData;
    private BigDecimal safetyStockQuantity;
    private BigDecimal standardDeviation;
    private BigDecimal safetyStockMultiplier;
    private String method;
    private String productSaleCluster;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "MENU_ITEM_ID", nullable = false, unique = true)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "PRODUCT_ID", nullable = false)
    public int getProductId() {
        return productId;
    }

    public void setProductId(int productId) {
        this.productId = productId;
    }

    @Column(name = "PRODUCT_NAME", nullable = false)
    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    @Column(name = "DIMENSION", nullable = true)
    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    @Column(name = "REQUESTED_QUANTITY", nullable = false)
    public BigDecimal getRequestedQuantity() {
        return requestedQuantity;
    }

    public void setRequestedQuantity(BigDecimal requestedQuantity) {
        this.requestedQuantity = requestedQuantity;
    }

    @Column(name = "REQUESTED_ABSOLUTE_QUANTITY", nullable = false)
    public BigDecimal getRequestedAbsoluteQuantity() {
        return requestedAbsoluteQuantity;
    }

    public void setRequestedAbsoluteQuantity(BigDecimal requestedAbsoluteQuantity) {
        this.requestedAbsoluteQuantity = requestedAbsoluteQuantity;
    }

    @Column(name = "TRANSFERRED_QUANTITY", nullable = true)
    public BigDecimal getTransferredQuantity() {
        return transferredQuantity;
    }

    public void setTransferredQuantity(BigDecimal transferredQuantity) {
        this.transferredQuantity = transferredQuantity;
    }

    @Column(name = "RECEIVED_QUANTITY", nullable = true)
    public BigDecimal getReceivedQuantity() {
        return receivedQuantity;
    }

    public void setReceivedQuantity(BigDecimal receivedQuantity) {
        this.receivedQuantity = receivedQuantity;
    }

    @OneToMany(fetch = FetchType.LAZY)
    @JoinColumn(name = "MENU_ITEM_ID")
    public List<ROMenuItemVariantData> getVariants() {
        return variants;
    }

    public void setVariants(List<ROMenuItemVariantData> variants) {
        this.variants = variants;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "REFERENCE_ORDER_ID", nullable = false)
    public ReferenceOrderData getReferenceOrderData() {
        return referenceOrderData;
    }

    public void setReferenceOrderData(ReferenceOrderData referenceOrderData) {
        this.referenceOrderData = referenceOrderData;
    }

    @Column(name = "SUGGESTED_QUANTITY", nullable = true)
    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    @Column(name = "DINE_IN_QUANTITY", nullable = true)
    public BigDecimal getDineInQuantity() {
        return dineInQuantity;
    }

    public void setDineInQuantity(BigDecimal dineInQuantity) {
        this.dineInQuantity = dineInQuantity;
    }

    @Column(name = "DELIVERY_QUANTITY", nullable = true)
    public BigDecimal getDeliveryQuantity() {
        return deliveryQuantity;
    }

    public void setDeliveryQuantity(BigDecimal deliveryQuantity) {
        this.deliveryQuantity = deliveryQuantity;
    }

    @Column(name = "TAKEAWAY_QUANTITY", nullable = true)
    public BigDecimal getTakeawayQuantity() {
        return takeawayQuantity;
    }

    public void setTakeawayQuantity(BigDecimal takeawayQuantity) {
        this.takeawayQuantity = takeawayQuantity;
    }

    @Column(name = "SAFETY_STOCK_QUANTITY", nullable = true)
    public BigDecimal getSafetyStockQuantity() {
        return safetyStockQuantity;
    }

    public void setSafetyStockQuantity(BigDecimal safetyStockQuantity) {
        this.safetyStockQuantity = safetyStockQuantity;
    }

    @Column(name = "STANDARD_DEVIATION", nullable = true)
    public BigDecimal getStandardDeviation() {
        return standardDeviation;
    }

    public void setStandardDeviation(BigDecimal standardDeviation) {
        this.standardDeviation = standardDeviation;
    }

    @Column(name = "SAFETY_STOCK_MULTIPLIER", nullable = true)
    public BigDecimal getSafetyStockMultiplier() {
        return safetyStockMultiplier;
    }

    public void setSafetyStockMultiplier(BigDecimal safetyStockMultiplier) {
        this.safetyStockMultiplier = safetyStockMultiplier;
    }

    @Column(name = "MEAN_METHOD", nullable = true)
    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    @Column(name = "PRODUCT_SALE_CLUSTER", nullable = true)
    public String getProductSaleCluster() {
        return productSaleCluster;
    }

    public void setProductSaleCluster(String productSaleCluster) {
        this.productSaleCluster = productSaleCluster;
    }
}
